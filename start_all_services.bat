@echo off
title RAGFlow Services Launcher
color 0A

echo ========================================
echo       RAGFlow 服务启动器
echo ========================================
echo.

echo [1] 启动所有服务（新窗口）
echo [2] 启动所有服务（当前终端）
echo [3] 仅启动 Task Executor
echo [4] 仅启动 RAGFlow Server  
echo [5] 仅启动 Web Server
echo [0] 退出
echo.

set /p choice="请选择操作 (0-5): "

if "%choice%"=="1" goto start_all_new_windows
if "%choice%"=="2" goto start_all_current
if "%choice%"=="3" goto start_task_executor
if "%choice%"=="4" goto start_ragflow_server
if "%choice%"=="5" goto start_web_server
if "%choice%"=="0" goto exit
goto invalid_choice

:start_all_new_windows
echo.
echo 正在启动所有服务（新窗口模式）...
echo.
echo 启动 Task Executor...
start "Task Executor" cmd /k call "%~dp0start_task_executor.bat"
timeout /t 2 /nobreak >nul

echo 启动 RAGFlow Server...
start "RAGFlow Server" cmd /k call "%~dp0start_ragflow_server.bat"
timeout /t 2 /nobreak >nul

echo 启动 Web Server...
start "Web Server" cmd /k call "%~dp0start_web_server.bat"

echo.
echo 所有服务已启动完成！
echo 请检查打开的终端窗口查看服务状态。
goto end

:start_all_current
echo.
echo 正在在当前终端启动服务...
echo 注意：这将阻塞当前终端
echo.
call "%~dp0start_task_executor.bat"
goto end

:start_task_executor
echo.
echo 启动 Task Executor...
start "Task Executor" cmd /k call "%~dp0start_task_executor.bat"
goto end

:start_ragflow_server
echo.
echo 启动 RAGFlow Server...
start "RAGFlow Server" cmd /k call "%~dp0start_ragflow_server.bat"
goto end

:start_web_server
echo.
echo 启动 Web Server...
start "Web Server" cmd /k call "%~dp0start_web_server.bat"
goto end

:invalid_choice
echo.
echo 无效选择，请重新运行脚本。
goto end

:end
echo.
pause

:exit
exit
