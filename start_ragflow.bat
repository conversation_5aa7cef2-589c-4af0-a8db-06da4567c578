@echo off
echo Starting all RAGFlow services...
echo.
echo Starting Task Executor...
start "Task Executor" cmd /k call "%~dp0start_task_executor.bat"
timeout /t 2 /nobreak >nul

echo Starting RAGFlow Server...
start "RAGFlow Server" cmd /k call "%~dp0start_ragflow_server.bat"
timeout /t 2 /nobreak >nul

echo Starting Web Server...
start "Web Server" cmd /k call "%~dp0start_web_server.bat"

echo.
echo All services started successfully!
echo Check the opened terminal windows for service status.
pause
