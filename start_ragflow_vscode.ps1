# RAGFlow VSCode 集成终端启动脚本
# 此脚本专为在VSCode集成终端中使用而设计

param(
    [switch]$Background,
    [switch]$Sequential
)

Write-Host "=== RAGFlow 服务启动器 ===" -ForegroundColor Magenta
Write-Host ""

# 获取脚本所在目录
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

if ($Background) {
    # 后台模式：在新的PowerShell窗口中启动服务
    Write-Host "后台模式：在新窗口中启动服务..." -ForegroundColor Green
    
    Write-Host "启动 Task Executor..." -ForegroundColor Yellow
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$scriptDir'; .\start_task_executor.bat"
    
    Start-Sleep -Seconds 2
    
    Write-Host "启动 RAGFlow Server..." -ForegroundColor Yellow
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$scriptDir'; .\start_ragflow_server.bat"
    
    Start-Sleep -Seconds 2
    
    Write-Host "启动 Web Server..." -ForegroundColor Yellow
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$scriptDir'; .\start_web_server.bat"
    
    Write-Host ""
    Write-Host "所有服务已在后台启动！" -ForegroundColor Green
    
} elseif ($Sequential) {
    # 顺序模式：在当前终端中依次启动服务
    Write-Host "顺序模式：在当前终端中启动服务..." -ForegroundColor Green
    Write-Host "注意：这将阻塞当前终端，建议使用后台模式" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "启动 Task Executor..." -ForegroundColor Yellow
    & "$scriptDir\start_task_executor.bat"
    
} else {
    # 默认模式：显示使用说明
    Write-Host "使用方法：" -ForegroundColor Cyan
    Write-Host "  .\start_ragflow_vscode.ps1 -Background    # 在新窗口中启动所有服务（推荐）"
    Write-Host "  .\start_ragflow_vscode.ps1 -Sequential   # 在当前终端中顺序启动服务"
    Write-Host ""
    Write-Host "推荐使用后台模式，这样可以保持VSCode终端可用。" -ForegroundColor Green
    Write-Host ""
    
    $choice = Read-Host "是否现在以后台模式启动所有服务？(y/n)"
    if ($choice -eq 'y' -or $choice -eq 'Y') {
        & $MyInvocation.MyCommand.Path -Background
    }
}
